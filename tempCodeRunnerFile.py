socketFd = nrc.connect_robot("192.168.1.13", "6001")

coord = 1
pos = nrc.VectorDouble()
for i in range(7):
    pos.append(0.0)
result_code = nrc.get_current_position(socketFd, coord, pos)
if result_code == 0:
    print(f"\n✅ 获取位置成功！")
    print(f"📍 当前位置: X={pos[0]:.3f}, Y={pos[1]:.3f}, Z={pos[2]:.3f}")
    print(f"📍 当前姿态: RX={pos[3]:.3f}, RY={pos[4]:.3f}, RZ={pos[5]:.3f}")
else:
    print(f"\n❌ 获取位置失败，错误代码: {result_code}")
nrc.disconnect_robot(socketFd)
print("\n🔗 连接已断开")