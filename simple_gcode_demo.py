#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版G-code XYZ运动演示

功能说明：
1. 读取jiyi.Gcode文件的前5个XYZ位置
2. 使用INEXBOT机械臂执行简单的运动演示
3. 专注于核心功能，代码简洁易懂

适用场景：
- 快速功能验证
- 学习和理解基本流程
- 小规模测试
"""

import sys
import os
import time
import re

# 添加lib目录到系统路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'lib'))

from config import ROBOT_IP, ROBOT_PORT
import nrc_interface as nrc

def parse_gcode_simple(filename, max_positions=5):
    """简单的G-code解析，只取前几个位置"""
    print(f"📖 解析G-code文件: {filename}")
    
    positions = []
    
    try:
        with open(filename, 'r', encoding='utf-8') as file:
            for line_num, line in enumerate(file, 1):
                line = line.strip()
                
                # 跳过空行和注释
                if not line or line.startswith(';'):
                    continue
                
                # 查找G0或G1指令
                if line.startswith('G0 ') or line.startswith('G1 '):
                    # 提取XYZ坐标
                    x_match = re.search(r'X([-+]?\d*\.?\d+)', line)
                    y_match = re.search(r'Y([-+]?\d*\.?\d+)', line)
                    z_match = re.search(r'Z([-+]?\d*\.?\d+)', line)
                    
                    if x_match and y_match and z_match:
                        x = float(x_match.group(1))
                        y = float(y_match.group(1))
                        z = float(z_match.group(1))
                        
                        positions.append((x, y, z))
                        print(f"  位置{len(positions)}: X={x:.3f}, Y={y:.3f}, Z={z:.3f}")
                        
                        # 达到最大数量就停止
                        if len(positions) >= max_positions:
                            break
        
        print(f"✅ 成功解析 {len(positions)} 个位置")
        return positions
        
    except Exception as e:
        print(f"❌ 解析失败: {e}")
        return []

def connect_robot():
    """连接并初始化机械臂"""
    print(f"🔗 连接机械臂 {ROBOT_IP}:{ROBOT_PORT}")
    
    # 连接
    socket_fd = nrc.connect_robot(ROBOT_IP, str(ROBOT_PORT))
    if socket_fd <= 0:
        print("❌ 连接失败")
        return None
    
    print("✅ 连接成功")
    
    try:
        # 初始化
        print("🔧 初始化机械臂...")
        nrc.clear_error(socket_fd)
        nrc.set_servo_state(socket_fd, 1)  # 就绪
        time.sleep(0.5)
        nrc.set_servo_poweron(socket_fd)   # 上电
        time.sleep(2)
        
        # 设置参数
        nrc.set_speed(socket_fd, 20)  # 20%安全速度
        nrc.set_current_coord(socket_fd, 3)  # 用户坐标系
        nrc.set_user_coord_number(socket_fd, 1)  # 用户坐标系1
        
        print("✅ 初始化完成")
        return socket_fd
        
    except Exception as e:
        print(f"❌ 初始化失败: {e}")
        nrc.disconnect_robot(socket_fd)
        return None

def get_current_position(socket_fd):
    """获取当前位置"""
    pos = nrc.VectorDouble()
    for i in range(7):
        pos.append(0.0)
    
    result = nrc.get_current_position(socket_fd, 3, pos)  # 用户坐标系
    if result == 0:
        return [pos[i] for i in range(6)]
    return None

def move_to_position(socket_fd, x, y, z, current_orientation):
    """移动到指定XYZ位置"""
    print(f"🎯 移动到: X={x:.3f}, Y={y:.3f}, Z={z:.3f}")
    
    try:
        # 创建移动命令
        move_cmd = nrc.MoveCmd()
        move_cmd.targetPosType = 1  # 笛卡尔坐标
        move_cmd.targetPosValue = nrc.VectorDouble()
        
        # 设置目标位置（保持当前姿态）
        target_pos = [x, y, z, current_orientation[3], current_orientation[4], current_orientation[5]]
        for pos_val in target_pos:
            move_cmd.targetPosValue.append(pos_val)
        
        # 设置运动参数
        move_cmd.coord = 3      # 用户坐标系
        move_cmd.velocity = 80  # 速度20%
        move_cmd.acc = 30       # 加速度30%
        move_cmd.dec = 30       # 减速度30%
        move_cmd.userNum = 1    # 用户坐标系1
        
        # 执行运动
        result = nrc.robot_movel(socket_fd, move_cmd)
        if result != 0:
            print(f"❌ 运动命令失败: {result}")
            return False
        
        # 等待运动完成
        print("⏳ 等待运动完成...")
        start_time = time.time()
        
        while time.time() - start_time < 30:  # 30秒超时
            running_status = 0
            result = nrc.get_robot_running_state(socket_fd, running_status)
            
            if isinstance(result, list) and len(result) > 1:
                if result[1] == 0:  # 停止状态
                    print("✅ 运动完成")
                    return True
            
            time.sleep(0.1)
        
        print("❌ 运动超时")
        return False
        
    except Exception as e:
        print(f"❌ 运动异常: {e}")
        return False

def disconnect_robot(socket_fd):
    """断开机械臂连接"""
    if socket_fd:
        try:
            print("🔌 下电并断开连接...")
            nrc.set_servo_poweroff(socket_fd)
            nrc.disconnect_robot(socket_fd)
            print("✅ 已断开连接")
        except Exception as e:
            print(f"⚠️ 断开连接错误: {e}")

def main():
    """主函数"""
    print("=" * 50)
    print("🤖 简化版G-code XYZ运动演示")
    print("=" * 50)
    
    # 1. 解析G-code文件（只取前5个位置）
    positions = parse_gcode_simple("jiyi.Gcode", max_positions=5)
    
    if not positions:
        print("❌ 未找到有效位置")
        return
    
    # 2. 显示将要执行的位置
    print(f"\n📋 将执行以下 {len(positions)} 个位置:")
    for i, (x, y, z) in enumerate(positions, 1):
        print(f"  {i}. X={x:8.3f}, Y={y:8.3f}, Z={z:8.3f}")
    
    # 3. 确认执行
    confirm = input(f"\n是否继续执行运动？(y/n): ").lower().strip()
    if confirm not in ['y', 'yes', '是']:
        print("👋 已取消")
        return
    
    # 4. 连接机械臂
    socket_fd = connect_robot()
    if not socket_fd:
        return
    
    try:
        # 5. 获取当前位置
        current_pos = get_current_position(socket_fd)
        if current_pos:
            print(f"\n📍 当前位置: X={current_pos[0]:.3f}, Y={current_pos[1]:.3f}, Z={current_pos[2]:.3f}")
        else:
            print("❌ 无法获取当前位置")
            return
        
        # 6. 执行运动
        print(f"\n🚀 开始执行运动...")
        
        success_count = 0
        for i, (x, y, z) in enumerate(positions, 1):
            print(f"\n--- 第 {i}/{len(positions)} 个位置 ---")
            
            if move_to_position(socket_fd, x, y, z, current_pos):
                success_count += 1
            else:
                print(f"❌ 第 {i} 个位置失败")
                break
            
            time.sleep(1)  # 停顿1秒
        
        # 7. 总结
        print(f"\n🎉 演示完成！")
        print(f"✅ 成功执行: {success_count}/{len(positions)} 个位置")
        
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断")
    except Exception as e:
        print(f"\n❌ 执行异常: {e}")
    finally:
        # 8. 清理
        disconnect_robot(socket_fd)
    
    print("=" * 50)

if __name__ == "__main__":
    main()
