# G-code XYZABC位置读取和运动控制器

## 项目简介

本项目实现了从G-code文件中读取XYZABC坐标位置，并使用INEXBOT机械臂在用户坐标系1(UCS1)中执行运动控制的功能。

## 功能特点

- ✅ **G-code解析**: 自动解析jiyi.Gcode文件中的XYZABC坐标
- ✅ **智能角度处理**: 提取ABC角度，没有角度的位置统一使用垂直朝下姿态
- ✅ **智能过滤**: 只提取G0和G1指令中的XYZABC位置，忽略其他参数
- ✅ **安全运动**: 使用用户坐标系1进行精确位置和姿态控制
- ✅ **实时监控**: 监控运动状态，确保每个位置都到达
- ✅ **错误处理**: 完善的错误处理和恢复机制
- ✅ **数据分析**: 提供详细的位置数据分析和安全检查

## 解析结果示例

从jiyi.Gcode文件中成功解析出34个XYZABC位置：

```
📊 位置数据分析:
  X轴范围:    0.000 ~  142.116 mm
  Y轴范围:    0.000 ~  175.278 mm  
  Z轴范围:    9.200 ~  249.899 mm
  A轴范围:   -0.855 ~   83.831 °
  B轴范围:   -0.291 ~    0.071 °
  C轴范围:  -90.399 ~   90.257 °
  包含角度的位置: 31/34 个
  垂直朝下位置: 3/34 个
  总移动距离: 521.650 mm
```

## 角度处理说明

### 自动角度检测
- **有ABC角度**: 直接使用G-code中的A、B、C值
- **无ABC角度**: 自动设置为垂直朝下姿态 (A=0°, B=0°, C=0°)

### 示例输出
```
第1个位置: X=0.000, Y=0.000, Z=249.899 (默认垂直朝下)
第4个位置: X=141.886, Y=164.811, Z=22.258, A=76.300, B=-0.000, C=90.073
```

## 使用方法

### 1. 测试G-code解析功能（推荐先运行）

```bash
python test_gcode_parser.py
```

这个命令会：
- 解析jiyi.Gcode文件
- 显示找到的XYZABC位置
- 分析坐标和角度范围
- 进行安全性检查
- **不需要连接机械臂**

### 2. 执行完整的运动控制

```bash
python gcode_xyz_reader_and_mover.py
```

这个命令会：
- 解析G-code文件
- 连接并初始化机械臂
- 在用户坐标系1中执行XYZABC运动
- 监控运动状态
- 提供执行统计

## 运动参数

### 机械臂配置
- **坐标系**: 用户坐标系1 (UCS1)
- **运动速度**: 100%（已优化）
- **运动类型**: 直线运动 (robot_movel)
- **姿态控制**: 支持完整的XYZABC六自由度控制

### 默认姿态
- **垂直朝下**: A=0°, B=0°, C=0°
- **适用场景**: 3D打印、点胶、激光切割等垂直工艺

## 安全注意事项

⚠️ **运行前必须检查**：

1. **机械臂状态**
   - 确保机械臂已开机并处于正常状态
   - 确认机械臂在安全位置
   - 检查急停按钮是否可用

2. **工作空间**
   - 确保工作空间内无障碍物
   - 确认坐标范围在机械臂工作空间内
   - 检查用户坐标系1是否正确设置
   - **特别注意角度范围是否在机械臂限位内**

3. **角度安全**
   - 检查ABC角度是否在机械臂允许范围内
   - 注意姿态变化可能导致的碰撞风险
   - 确认工具末端的安全距离

## 技术特点

### G-code解析器增强

- 使用正则表达式精确提取XYZABC坐标
- 支持正负数和小数格式
- 自动检测是否包含ABC角度
- 智能默认姿态设置

### 运动控制器增强

- 新增`move_to_xyzabc_position()`方法
- 保持向后兼容的`move_to_xyz_position()`方法
- 完整的六自由度控制
- 实时姿态显示

## 程序输出示例

### 解析阶段
```
📖 开始解析G-code文件: jiyi.Gcode
  第1个位置: X=0.000, Y=0.000, Z=249.899 (默认垂直朝下)
  第4个位置: X=141.886, Y=164.811, Z=22.258, A=76.300, B=-0.000, C=90.073
✅ 解析完成，共找到 34 个XYZABC位置
```

### 运动阶段
```
🎯 移动到位置: X=141.886, Y=164.811, Z=22.258
   目标姿态: A=76.300°, B=-0.000°, C=90.073°
✅ 运动完成
```

## 扩展功能

可以轻松扩展的功能：

1. **支持更多G-code指令** (G2, G3圆弧等)
2. **速度控制** (F参数)
3. **工具控制** (T参数)
4. **多文件批处理**
5. **轨迹优化**

## 故障排除

### 常见问题

1. **"角度超出范围"**
   - 检查机械臂角度限位
   - 确认用户坐标系设置
   - 验证G-code中的角度值

2. **"姿态不可达"**
   - 检查目标姿态是否在工作空间内
   - 确认是否存在奇异点
   - 尝试调整接近路径

## 开发者信息

- 基于INEXBOT 3DP2项目
- 使用NRC Python接口
- 支持完整的六自由度控制
- 支持Python 3.7+

---

**🎯 快速开始**: 运行 `python test_gcode_parser.py` 开始测试XYZABC解析功能！
