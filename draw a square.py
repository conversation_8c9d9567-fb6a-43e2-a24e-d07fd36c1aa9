#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
INEXBOT机械臂 - 画正方形程序 (最终版 - 基于位置等待)
这是最健壮的版本，通过检查坐标是否到达目标来确认移动完成，
解决了所有已知的竞争条件和时序问题。
"""

import sys
import os
import time
import math # 需要导入math库来计算距离

# 假设lib文件夹与此脚本在同一目录下
sys.path.append(os.path.join(os.path.dirname(__file__), 'lib'))

from config import ROBOT_IP, ROBOT_PORT
import nrc_interface as nrc

# --- 全局常量 ---
# 定义到位判断的容差 (单位: mm)。当机器人与目标点距离小于此值时，认为已到达。
# 这个值需要根据您的精度要求调整，0.5mm对于一般应用是合理的。
POSITION_TOLERANCE_MM = 0.5 
# 定义等待超时的秒数，防止因机器人无法到达目标点而无限等待
WAIT_TIMEOUT_S = 15.0 

# --- 辅助函数：计算两点之间的欧式距离 ---
def calculate_distance(p1, p2):
    """计算两个三维坐标点 p1 和 p2 之间的距离"""
    return math.sqrt((p1[0] - p2[0])**2 + (p1[1] - p2[1])**2 + (p1[2] - p2[2])**2)

# --- 核心辅助函数：基于位置的等待机制 ---
def wait_until_position_reached(socket_fd, target_point_cmd):
    """
    等待直到机器人末端到达目标点附近。这是最可靠的等待方式。
    :param socket_fd: 机器人连接句柄
    :param target_point_cmd: 包含目标坐标和坐标系信息的MoveCmd对象
    """
    print("      - 正在等待机器人移动到位...")
    
    start_time = time.time()
    target_pos = list(target_point_cmd.targetPosValue)
    
    while True:
        # 1. 检查是否超时
        if time.time() - start_time > WAIT_TIMEOUT_S:
            raise TimeoutError(f"等待机器人到达目标点超时（超过 {WAIT_TIMEOUT_S} 秒）！")

        # 2. 获取当前位置
        current_pos_vec = nrc.VectorDouble()
        # **关键**: 必须在与目标点相同的坐标系下获取当前位置！
        result = nrc.get_current_position(socket_fd, target_point_cmd.coord, current_pos_vec)
        if result != 0:
            print(f"      - 警告: 获取当前位置失败，错误码: {result}。稍后重试...")
            time.sleep(0.2)
            continue
        
        current_pos = list(current_pos_vec)
        
        # 3. 计算距离
        distance = calculate_distance(current_pos, target_pos)
        
        # 4. 判断是否已到达
        if distance < POSITION_TOLERANCE_MM:
            print(f"      - 到位成功！与目标距离: {distance:.3f} mm。")
            break # 退出循环，移动完成
        
        # 打印调试信息（可选）
        # print(f"      - 当前距离目标: {distance:.2f} mm") 

        # 5. 短暂休眠，避免频繁查询刷爆控制器
        time.sleep(0.1)


def draw_simple_square_final():
    """
    连接机器人并画一个正方形。使用最可靠的基于位置的等待方法。
    """
    
    # --- 1. 定义参数 ---
    USER_COORD_NUM = 1
    SIDE_LENGTH = 50.0
    SPEED = 60

    socket_fd = -1

    try:
        # --- 2. 连接与准备 ---
        print(f"🔗 正在连接机器人 {ROBOT_IP}...")
        socket_fd = nrc.connect_robot(ROBOT_IP, str(ROBOT_PORT))
        if socket_fd <= 0: raise ConnectionError("连接机器人失败！")

        print("✅ 连接成功。正在上电...")
        nrc.clear_error(socket_fd)
        nrc.set_servo_state(socket_fd, 1)
        time.sleep(0.5)
        nrc.set_servo_poweron(socket_fd)
        time.sleep(2)

        # --- 3. 设置工作模式和坐标系 ---
        print(f"🔩 设置全局速度为 {SPEED}%，并激活用户坐标系 {USER_COORD_NUM}...")
        nrc.set_speed(socket_fd, SPEED)
        nrc.set_current_coord(socket_fd, 3)
        nrc.set_user_coord_number(socket_fd, USER_COORD_NUM)
        
        # --- 4. 获取起点并计算路径 ---
        print(f"📍 获取当前位置作为正方形的第一个角(P1)...")
        pos_vec = nrc.VectorDouble()
        result = nrc.get_current_position(socket_fd, 3, pos_vec) 
        if result != 0: raise RuntimeError(f"获取当前位置失败！错误码: {result}")
        
        p1 = list(pos_vec)
        while len(p1) < 6: p1.append(0.0)
        
        print(f"  -> P1 基准点 (用户坐标系 {USER_COORD_NUM}): {[f'{x:.2f}' for x in p1[:3]]}")

        p2 = p1[:]; p2[0] += SIDE_LENGTH
        p3 = p2[:]; p3[1] += SIDE_LENGTH
        p4 = p3[:]; p4[0] -= SIDE_LENGTH

        path = [p2, p3, p4, p1]
        point_names = ["P2", "P3", "P4", "P1(终点)"]
        
        # --- 5. 执行移动 ---
        print(f"\n✍️ 开始在用户坐标系 {USER_COORD_NUM} 中绘制正方形...")
        for i, point in enumerate(path):
            print(f"  -> 正在发送移动指令: {point_names[i]} {[f'{p:.2f}' for p in point[:3]]}")
            
            move_cmd = nrc.MoveCmd()
            move_cmd.coord = 3
            move_cmd.userNum = USER_COORD_NUM
            move_cmd.velocity = SPEED
            move_cmd.targetPosType = 1
            move_cmd.targetPosValue = nrc.VectorDouble(point)

            nrc.robot_movel(socket_fd, move_cmd)

            # 使用终极等待函数，确保机器人移动到位后再进行下一步
            wait_until_position_reached(socket_fd, move_cmd)

        print("\n✅ 正方形绘制完成！")

    except Exception as e:
        print(f"💥 程序出错: {e}")

    finally:
        # --- 6. 清理与断开 ---
        if socket_fd > 0:
            print("🔋 正在下电并断开连接...")
            nrc.set_servo_poweroff(socket_fd)
            time.sleep(1)
            nrc.disconnect_robot(socket_fd)
            print("🔌 连接已断开。")

if __name__ == "__main__":
    print("======================================================")
    print("      INEXBOT 机器人画正方形程序 (最终可靠版)      ")
    print("======================================================")
    print("!! 警告: 即将运行机器人移动程序。")
    print("!! 前提1: 确保机器人周围空间安全，无障碍物。")
    
    input("确认安全并满足前提后，请按 Enter 键继续...")
    
    draw_simple_square_final()