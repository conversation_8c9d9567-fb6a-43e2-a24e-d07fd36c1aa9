#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
G-code解析器测试脚本

功能说明：
1. 测试G-code文件解析功能
2. 验证XYZ坐标提取的准确性
3. 分析位置数据的特征
4. 不需要连接实际机械臂，仅测试解析功能

使用方法：
python test_gcode_parser.py
"""

import sys
import os

# 添加当前目录到系统路径以导入gcode_xyz_reader_and_mover模块
sys.path.append(os.path.dirname(__file__))

from gcode_xyz_reader_and_mover import GCodeXYZReader, preview_positions, analyze_positions

def test_gcode_parsing():
    """测试G-code解析功能"""
    print("=" * 60)
    print("🧪 G-code解析器测试")
    print("=" * 60)
    
    # 1. 创建G-code读取器
    gcode_file = "jiyi.Gcode"
    print(f"📖 测试文件: {gcode_file}")
    
    if not os.path.exists(gcode_file):
        print(f"❌ 文件不存在: {gcode_file}")
        return False
    
    # 2. 解析文件
    reader = GCodeXYZReader(gcode_file)
    xyz_positions = reader.parse_gcode_file()
    
    if not xyz_positions:
        print("❌ 解析失败或未找到XYZ坐标")
        return False
    
    # 3. 显示解析结果
    print(f"\n✅ 解析成功！共找到 {len(xyz_positions)} 个XYZ位置")
    
    # 4. 预览位置数据
    preview_positions(xyz_positions, max_preview=10)

    # 5. 分析位置数据
    analyze_positions(xyz_positions)
    
    # 6. 详细分析
    print(f"\n🔍 详细分析:")

    # 检查坐标范围
    x_coords = [pos[0] for pos in xyz_positions]
    y_coords = [pos[1] for pos in xyz_positions]
    z_coords = [pos[2] for pos in xyz_positions]
    a_coords = [pos[3] for pos in xyz_positions]
    b_coords = [pos[4] for pos in xyz_positions]
    c_coords = [pos[5] for pos in xyz_positions]

    print(f"  X坐标统计:")
    print(f"    最小值: {min(x_coords):8.3f} mm")
    print(f"    最大值: {max(x_coords):8.3f} mm")
    print(f"    平均值: {sum(x_coords)/len(x_coords):8.3f} mm")

    print(f"  Y坐标统计:")
    print(f"    最小值: {min(y_coords):8.3f} mm")
    print(f"    最大值: {max(y_coords):8.3f} mm")
    print(f"    平均值: {sum(y_coords)/len(y_coords):8.3f} mm")

    print(f"  Z坐标统计:")
    print(f"    最小值: {min(z_coords):8.3f} mm")
    print(f"    最大值: {max(z_coords):8.3f} mm")
    print(f"    平均值: {sum(z_coords)/len(z_coords):8.3f} mm")

    print(f"  A角度统计:")
    print(f"    最小值: {min(a_coords):8.3f} °")
    print(f"    最大值: {max(a_coords):8.3f} °")
    print(f"    平均值: {sum(a_coords)/len(a_coords):8.3f} °")

    print(f"  B角度统计:")
    print(f"    最小值: {min(b_coords):8.3f} °")
    print(f"    最大值: {max(b_coords):8.3f} °")
    print(f"    平均值: {sum(b_coords)/len(b_coords):8.3f} °")

    print(f"  C角度统计:")
    print(f"    最小值: {min(c_coords):8.3f} °")
    print(f"    最大值: {max(c_coords):8.3f} °")
    print(f"    平均值: {sum(c_coords)/len(c_coords):8.3f} °")
    
    # 7. 检查坐标变化
    print(f"\n📈 坐标变化分析:")
    
    if len(xyz_positions) > 1:
        # 计算最大单步移动距离
        max_distance = 0.0
        max_distance_step = 0
        
        for i in range(1, len(xyz_positions)):
            prev_pos = xyz_positions[i-1]
            curr_pos = xyz_positions[i]
            
            dx = curr_pos[0] - prev_pos[0]
            dy = curr_pos[1] - prev_pos[1]
            dz = curr_pos[2] - prev_pos[2]
            
            distance = (dx**2 + dy**2 + dz**2)**0.5
            
            if distance > max_distance:
                max_distance = distance
                max_distance_step = i
        
        print(f"  最大单步移动距离: {max_distance:.3f} mm (第{max_distance_step}步)")
        
        # 显示前几步的移动
        print(f"  前5步移动详情:")
        for i in range(1, min(6, len(xyz_positions))):
            prev_pos = xyz_positions[i-1]
            curr_pos = xyz_positions[i]

            dx = curr_pos[0] - prev_pos[0]
            dy = curr_pos[1] - prev_pos[1]
            dz = curr_pos[2] - prev_pos[2]
            distance = (dx**2 + dy**2 + dz**2)**0.5

            print(f"    步骤{i}: ΔX={dx:7.3f}, ΔY={dy:7.3f}, ΔZ={dz:7.3f}, 距离={distance:7.3f} mm")
    
    # 8. 安全性检查
    print(f"\n🛡️ 安全性检查:")
    
    # 检查是否有异常大的坐标值
    warning_threshold = 1000.0  # 1米
    large_coords = []

    for i, pos in enumerate(xyz_positions):
        x, y, z = pos[0], pos[1], pos[2]
        if abs(x) > warning_threshold or abs(y) > warning_threshold or abs(z) > warning_threshold:
            large_coords.append((i+1, x, y, z))

    if large_coords:
        print(f"  ⚠️ 发现 {len(large_coords)} 个可能异常的大坐标值:")
        for step, x, y, z in large_coords[:5]:  # 只显示前5个
            print(f"    第{step}步: X={x:.3f}, Y={y:.3f}, Z={z:.3f}")
        if len(large_coords) > 5:
            print(f"    ... 还有 {len(large_coords)-5} 个")
    else:
        print(f"  ✅ 所有坐标值都在合理范围内 (< {warning_threshold} mm)")

    # 检查Z轴是否有负值
    negative_z = [pos for pos in xyz_positions if pos[2] < 0]
    if negative_z:
        print(f"  ⚠️ 发现 {len(negative_z)} 个负Z坐标值，请注意安全")
    else:
        print(f"  ✅ 所有Z坐标都为正值")

    # 检查角度范围
    large_angles = []
    for i, pos in enumerate(xyz_positions):
        a, b, c = pos[3], pos[4], pos[5]
        if abs(a) > 180 or abs(b) > 180 or abs(c) > 180:
            large_angles.append((i+1, a, b, c))

    if large_angles:
        print(f"  ⚠️ 发现 {len(large_angles)} 个可能异常的角度值:")
        for step, a, b, c in large_angles[:5]:
            print(f"    第{step}步: A={a:.3f}°, B={b:.3f}°, C={c:.3f}°")
    else:
        print(f"  ✅ 所有角度值都在合理范围内 (< 180°)")
    
    return True

def show_sample_gcode_lines():
    """显示G-code文件的示例行"""
    print(f"\n📄 G-code文件内容示例:")
    
    try:
        with open("jiyi.Gcode", 'r', encoding='utf-8') as file:
            lines = file.readlines()
        
        # 显示前10行
        print(f"  文件前10行:")
        for i, line in enumerate(lines[:10], 1):
            line = line.strip()
            if line:
                print(f"    {i:2d}: {line}")
        
        # 查找并显示包含XYZ的行
        xyz_lines = []
        for i, line in enumerate(lines, 1):
            line = line.strip()
            if line.startswith('G0 ') or line.startswith('G1 '):
                if 'X' in line and 'Y' in line and 'Z' in line:
                    xyz_lines.append((i, line))
        
        if xyz_lines:
            print(f"\n  包含XYZ坐标的行示例 (前5行):")
            for line_num, line in xyz_lines[:5]:
                print(f"    {line_num:2d}: {line}")
        
    except Exception as e:
        print(f"  ❌ 读取文件时发生错误: {e}")

def main():
    """主函数"""
    print("🚀 开始G-code解析器测试...\n")
    
    # 显示文件内容示例
    show_sample_gcode_lines()
    
    # 测试解析功能
    success = test_gcode_parsing()
    
    if success:
        print(f"\n" + "=" * 60)
        print("🎉 测试完成！")
        print("✅ G-code解析功能正常")
        print("✅ XYZ坐标提取成功")
        print("✅ 数据分析完成")
        print("\n💡 下一步:")
        print("  • 检查坐标范围是否合理")
        print("  • 确认机械臂工作空间")
        print("  • 运行完整的运动控制程序")
    else:
        print(f"\n" + "=" * 60)
        print("❌ 测试失败")
        print("💡 请检查:")
        print("  • jiyi.Gcode文件是否存在")
        print("  • 文件格式是否正确")
        print("  • 文件中是否包含有效的G-code指令")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
