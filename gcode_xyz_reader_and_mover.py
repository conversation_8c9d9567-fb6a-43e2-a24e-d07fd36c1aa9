#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
G-code XYZ位置读取和运动控制器

功能说明：
1. 读取jiyi.Gcode文件中的XYZ坐标位置
2. 解析G-code指令中的X、Y、Z参数
3. 使用INEXBOT机械臂在用户坐标系1(UCS1)中执行运动
4. 忽略其他参数（A、B、C、E、F等），专注于XYZ位置控制

基于项目现有的用户坐标系运动功能实现。
"""

import sys
import os
import time
import re
from typing import List, Tuple, Optional

# 添加lib目录到系统路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'lib'))

from config import ROBOT_IP, ROBOT_PORT
import nrc_interface as nrc

class GCodeXYZReader:
    """G-code XYZ坐标读取器"""
    
    def __init__(self, gcode_file_path: str):
        self.gcode_file_path = gcode_file_path
        self.xyz_positions = []
        
    def parse_gcode_file(self) -> List[Tuple[float, float, float, float, float, float]]:
        """
        解析G-code文件，提取XYZ坐标和ABC角度

        Returns:
            List[Tuple[float, float, float, float, float, float]]: XYZABC坐标列表
        """
        print(f"📖 开始解析G-code文件: {self.gcode_file_path}")

        try:
            with open(self.gcode_file_path, 'r', encoding='utf-8') as file:
                lines = file.readlines()

            xyzabc_positions = []
            line_count = 0

            for line_num, line in enumerate(lines, 1):
                line = line.strip()

                # 跳过空行和注释行
                if not line or line.startswith(';'):
                    continue

                # 查找包含XYZ坐标的G指令
                if self._is_movement_command(line):
                    xyzabc = self._extract_xyz_from_line(line)
                    if xyzabc:
                        xyzabc_positions.append(xyzabc)
                        line_count += 1
                        # 检查是否包含ABC角度
                        has_abc = any(abs(angle) > 0.001 for angle in xyzabc[3:6])
                        if has_abc:
                            print(f"  第{line_count}个位置: X={xyzabc[0]:.3f}, Y={xyzabc[1]:.3f}, Z={xyzabc[2]:.3f}, A={xyzabc[3]:.3f}, B={xyzabc[4]:.3f}, C={xyzabc[5]:.3f}")
                        else:
                            print(f"  第{line_count}个位置: X={xyzabc[0]:.3f}, Y={xyzabc[1]:.3f}, Z={xyzabc[2]:.3f} (默认垂直朝下)")

            self.xyz_positions = xyzabc_positions
            print(f"✅ 解析完成，共找到 {len(xyzabc_positions)} 个XYZABC位置")
            return xyzabc_positions
            
        except FileNotFoundError:
            print(f"❌ 文件未找到: {self.gcode_file_path}")
            return []
        except Exception as e:
            print(f"❌ 解析文件时发生错误: {e}")
            return []
    
    def _is_movement_command(self, line: str) -> bool:
        """判断是否为运动指令"""
        # G0 (快速移动) 和 G1 (直线插补) 指令
        return line.startswith('G0 ') or line.startswith('G1 ')
    
    def _extract_xyz_from_line(self, line: str) -> Optional[Tuple[float, float, float, float, float, float]]:
        """从G-code行中提取XYZ坐标和ABC角度"""
        try:
            # 使用正则表达式提取X、Y、Z值
            x_match = re.search(r'X([-+]?\d*\.?\d+)', line)
            y_match = re.search(r'Y([-+]?\d*\.?\d+)', line)
            z_match = re.search(r'Z([-+]?\d*\.?\d+)', line)

            # 提取A、B、C角度值（可选）
            a_match = re.search(r'A([-+]?\d*\.?\d+)', line)
            b_match = re.search(r'B([-+]?\d*\.?\d+)', line)
            c_match = re.search(r'C([-+]?\d*\.?\d+)', line)

            # 只有当X、Y、Z都存在时才返回坐标
            if x_match and y_match and z_match:
                x = float(x_match.group(1))
                y = float(y_match.group(1))
                z = float(z_match.group(1))

                # 提取ABC角度，如果不存在则使用默认的垂直朝下姿态
                # 垂直朝下姿态: A=0°, B=0°, C=0° (根据机械臂配置可能需要调整)
                a = float(a_match.group(1)) if a_match else 0.0
                b = float(b_match.group(1)) if b_match else 0.0
                c = float(c_match.group(1)) if c_match else 0.0

                return (x, y, z, a, b, c)

        except (ValueError, AttributeError) as e:
            print(f"⚠️ 解析坐标时出错: {line} - {e}")

        return None

class RobotXYZMover:
    """机械臂XYZ位置运动控制器"""
    
    def __init__(self):
        self.socket_fd = None
        self.user_coord_num = 1  # 使用用户坐标系1
        
    def connect_and_initialize(self) -> bool:
        """连接并初始化机械臂"""
        print(f"🔗 连接机械臂 {ROBOT_IP}:{ROBOT_PORT}...")
        
        try:
            # 连接机械臂
            self.socket_fd = nrc.connect_robot(ROBOT_IP, str(ROBOT_PORT))
            if self.socket_fd <= 0:
                print("❌ 连接失败")
                return False
                
            print("✅ 连接成功")
            
            # 初始化机械臂
            print("🔧 初始化机械臂...")
            nrc.clear_error(self.socket_fd)
            nrc.set_servo_state(self.socket_fd, 1)  # 设置为就绪状态
            time.sleep(0.5)
            nrc.set_servo_poweron(self.socket_fd)   # 上电
            time.sleep(2)
            
            # 设置运动参数
            nrc.set_speed(self.socket_fd, 30)  # 设置30%速度（安全速度）
            nrc.set_current_coord(self.socket_fd, 2)  # 设置为用户坐标系
            nrc.set_user_coord_number(self.socket_fd, self.user_coord_num)
            
            print("✅ 机械臂初始化完成")
            return True
            
        except Exception as e:
            print(f"❌ 初始化失败: {e}")
            return False
    
    def get_current_position(self) -> Optional[List[float]]:
        """获取当前位置"""
        try:
            pos = nrc.VectorDouble()
            for i in range(7):
                pos.append(0.0)
            
            result = nrc.get_current_position(self.socket_fd, 3, pos)  # 3=用户坐标系
            if result == 0:
                return [pos[i] for i in range(6)]  # 返回X,Y,Z,RX,RY,RZ
            else:
                print(f"❌ 获取当前位置失败，错误码: {result}")
                return None
                
        except Exception as e:
            print(f"❌ 获取位置时发生异常: {e}")
            return None
    
    def move_to_xyzabc_position(self, x: float, y: float, z: float,
                              a: float = 0.0, b: float = 0.0, c: float = 0.0) -> bool:
        """
        移动到指定XYZABC位置

        Args:
            x, y, z: 目标位置坐标
            a, b, c: 目标姿态角度（默认为垂直朝下）
        """
        try:
            # 构建目标位置（包含姿态）
            target_pos = [x, y, z, a, b, c]

            print(f"🎯 移动到位置: X={x:.3f}, Y={y:.3f}, Z={z:.3f}")
            if abs(a) > 0.001 or abs(b) > 0.001 or abs(c) > 0.001:
                print(f"   目标姿态: A={a:.3f}°, B={b:.3f}°, C={c:.3f}°")
            else:
                print(f"   目标姿态: 垂直朝下 (A=0°, B=0°, C=0°)")

            # 创建移动命令
            move_cmd = nrc.MoveCmd()
            move_cmd.targetPosType = 1  # 1=笛卡尔坐标
            move_cmd.targetPosValue = nrc.VectorDouble()

            for pos_val in target_pos:
                move_cmd.targetPosValue.append(pos_val)

            # 设置运动参数
            move_cmd.coord = 3          # 用户坐标系
            move_cmd.velocity = 100     # 速度100%
            move_cmd.acc = 40          # 加速度40%
            move_cmd.dec = 40          # 减速度40%
            move_cmd.pl = 0            # 平滑度
            move_cmd.time = 0          # 时间(0表示不限制)
            move_cmd.toolNum = 0       # 工具号
            move_cmd.userNum = self.user_coord_num  # 用户坐标系编号

            # 执行运动
            result = nrc.robot_movel(self.socket_fd, move_cmd)
            if result != 0:
                print(f"❌ 移动命令发送失败，错误码: {result}")
                return False

            # 等待运动完成
            return self._wait_for_motion_complete()

        except Exception as e:
            print(f"❌ 移动过程中发生异常: {e}")
            return False

    def move_to_xyz_position(self, x: float, y: float, z: float,
                           keep_orientation: bool = True) -> bool:
        """
        移动到指定XYZ位置（保持兼容性的方法）

        Args:
            x, y, z: 目标位置坐标
            keep_orientation: 是否保持当前姿态
        """
        if keep_orientation:
            # 获取当前位置以保持姿态
            current_pos = self.get_current_position()
            if current_pos is None:
                return False
            return self.move_to_xyzabc_position(x, y, z, current_pos[3], current_pos[4], current_pos[5])
        else:
            # 使用默认的垂直朝下姿态
            return self.move_to_xyzabc_position(x, y, z, 0.0, 0.0, 0.0)
    
    def _wait_for_motion_complete(self, timeout_seconds: int = 30) -> bool:
        """等待运动完成"""
        start_time = time.time()
        
        while time.time() - start_time < timeout_seconds:
            try:
                running_status = 0
                result = nrc.get_robot_running_state(self.socket_fd, running_status)
                
                if isinstance(result, list) and len(result) > 1:
                    status = result[1]
                    if status == 0:  # 0=停止，运动完成
                        print("✅ 运动完成")
                        return True
                    elif status == 1:  # 1=运行中
                        print("🔄 运动中...", end='\r')
                        time.sleep(0.1)
                        continue
                
                time.sleep(0.1)
                
            except Exception as e:
                print(f"❌ 检查运动状态时发生异常: {e}")
                return False
        
        print(f"❌ 运动超时 ({timeout_seconds}秒)")
        return False
    
    def disconnect(self):
        """断开连接并下电"""
        if self.socket_fd:
            try:
                print("🔌 机械臂下电并断开连接...")
                nrc.set_servo_poweroff(self.socket_fd)
                nrc.disconnect_robot(self.socket_fd)
                print("✅ 已安全断开连接")
            except Exception as e:
                print(f"⚠️ 断开连接时发生错误: {e}")

def preview_positions(xyzabc_positions: List[Tuple[float, float, float, float, float, float]], max_preview: int = 5):
    """预览位置数据"""
    print(f"\n📋 位置数据预览 (显示前{min(max_preview, len(xyzabc_positions))}个):")
    for i, pos in enumerate(xyzabc_positions[:max_preview], 1):
        x, y, z, a, b, c = pos
        has_abc = any(abs(angle) > 0.001 for angle in [a, b, c])
        if has_abc:
            print(f"  {i:2d}. X={x:8.3f}, Y={y:8.3f}, Z={z:8.3f}, A={a:7.3f}°, B={b:7.3f}°, C={c:7.3f}°")
        else:
            print(f"  {i:2d}. X={x:8.3f}, Y={y:8.3f}, Z={z:8.3f} (垂直朝下)")

    if len(xyzabc_positions) > max_preview:
        print(f"  ... 还有 {len(xyzabc_positions) - max_preview} 个位置")

def analyze_positions(xyzabc_positions: List[Tuple[float, float, float, float, float, float]]):
    """分析位置数据的范围和特征"""
    if not xyzabc_positions:
        return

    x_coords = [pos[0] for pos in xyzabc_positions]
    y_coords = [pos[1] for pos in xyzabc_positions]
    z_coords = [pos[2] for pos in xyzabc_positions]
    a_coords = [pos[3] for pos in xyzabc_positions]
    b_coords = [pos[4] for pos in xyzabc_positions]
    c_coords = [pos[5] for pos in xyzabc_positions]

    print(f"\n📊 位置数据分析:")
    print(f"  X轴范围: {min(x_coords):8.3f} ~ {max(x_coords):8.3f} mm")
    print(f"  Y轴范围: {min(y_coords):8.3f} ~ {max(y_coords):8.3f} mm")
    print(f"  Z轴范围: {min(z_coords):8.3f} ~ {max(z_coords):8.3f} mm")
    print(f"  A轴范围: {min(a_coords):8.3f} ~ {max(a_coords):8.3f} °")
    print(f"  B轴范围: {min(b_coords):8.3f} ~ {max(b_coords):8.3f} °")
    print(f"  C轴范围: {min(c_coords):8.3f} ~ {max(c_coords):8.3f} °")

    # 统计有角度的位置数量
    positions_with_abc = sum(1 for pos in xyzabc_positions
                           if any(abs(angle) > 0.001 for angle in pos[3:6]))
    print(f"  包含角度的位置: {positions_with_abc}/{len(xyzabc_positions)} 个")
    print(f"  垂直朝下位置: {len(xyzabc_positions) - positions_with_abc}/{len(xyzabc_positions)} 个")

    # 计算移动距离
    total_distance = 0.0
    for i in range(1, len(xyzabc_positions)):
        prev_pos = xyzabc_positions[i-1]
        curr_pos = xyzabc_positions[i]
        distance = ((curr_pos[0] - prev_pos[0])**2 +
                   (curr_pos[1] - prev_pos[1])**2 +
                   (curr_pos[2] - prev_pos[2])**2)**0.5
        total_distance += distance

    print(f"  总移动距离: {total_distance:.3f} mm")
    print(f"  平均每步距离: {total_distance/max(1, len(xyzabc_positions)-1):.3f} mm")

def main():
    """主函数"""
    print("=" * 60)
    print("🤖 G-code XYZ位置读取和运动控制器")
    print("=" * 60)
    print("📝 功能说明:")
    print("  • 读取jiyi.Gcode文件中的XYZ坐标")
    print("  • 使用INEXBOT机械臂在用户坐标系1中执行运动")
    print("  • 忽略其他参数，专注于XYZ位置控制")
    print("=" * 60)

    # 1. 解析G-code文件
    gcode_reader = GCodeXYZReader("jiyi.Gcode")
    xyzabc_positions = gcode_reader.parse_gcode_file()

    if not xyzabc_positions:
        print("❌ 未找到有效的XYZABC位置数据")
        print("💡 请检查:")
        print("  • jiyi.Gcode文件是否存在")
        print("  • 文件中是否包含G0或G1指令")
        print("  • 指令中是否包含完整的XYZ坐标")
        return

    # 2. 分析和预览位置数据
    preview_positions(xyzabc_positions)
    analyze_positions(xyzabc_positions)

    # 3. 安全确认
    print(f"\n⚠️ 安全提醒:")
    print(f"  • 请确保机械臂处于安全位置")
    print(f"  • 请确保工作空间内无障碍物")
    print(f"  • 运动过程中请保持监控")
    print(f"  • 如有异常请立即按急停按钮")
    print(f"  • 注意：没有ABC角度的位置将使用垂直朝下姿态")

    # 询问用户是否继续
    user_input = input(f"\n是否继续执行 {len(xyzabc_positions)} 个位置的运动？(y/n): ").lower().strip()
    if user_input not in ['y', 'yes', '是']:
        print("👋 操作已取消")
        return

    # 4. 初始化机械臂
    robot_mover = RobotXYZMover()

    try:
        if not robot_mover.connect_and_initialize():
            print("❌ 机械臂初始化失败")
            print("💡 请检查:")
            print("  • 机械臂是否开机")
            print("  • 网络连接是否正常")
            print("  • IP地址配置是否正确")
            return

        # 5. 显示当前位置
        current_pos = robot_mover.get_current_position()
        if current_pos:
            print(f"\n📍 当前位置: X={current_pos[0]:.3f}, Y={current_pos[1]:.3f}, Z={current_pos[2]:.3f}")
            print(f"📍 当前姿态: RX={current_pos[3]:.3f}, RY={current_pos[4]:.3f}, RZ={current_pos[5]:.3f}")

        # 6. 执行运动序列
        print(f"\n🚀 开始执行运动序列...")
        print(f"⏱️ 预计用时: {len(xyzabc_positions) * 2:.0f}-{len(xyzabc_positions) * 5:.0f} 秒")

        success_count = 0
        start_time = time.time()

        for i, pos in enumerate(xyzabc_positions, 1):
            x, y, z, a, b, c = pos
            print(f"\n--- 第 {i}/{len(xyzabc_positions)} 个位置 ---")

            if robot_mover.move_to_xyzabc_position(x, y, z, a, b, c):
                success_count += 1
                elapsed = time.time() - start_time
                print(f"✅ 第 {i} 个位置移动成功 (用时: {elapsed:.1f}s)")
            else:
                print(f"❌ 第 {i} 个位置移动失败")

                # 询问是否继续
                continue_input = input("是否继续下一个位置？(y/n): ").lower().strip()
                if continue_input not in ['y', 'yes', '是']:
                    print("🛑 用户选择停止执行")
                    break

            # 短暂停顿
            time.sleep(0.5)

        # 7. 总结
        total_time = time.time() - start_time
        print(f"\n" + "=" * 60)
        print(f"🎉 运动序列执行完成！")
        print(f"✅ 成功执行: {success_count}/{len(xyzabc_positions)} 个位置")
        print(f"📊 成功率: {success_count/len(xyzabc_positions)*100:.1f}%")
        print(f"⏱️ 总用时: {total_time:.1f} 秒")
        print(f"⚡ 平均每个位置: {total_time/len(xyzabc_positions):.1f} 秒")

    except KeyboardInterrupt:
        print("\n⚠️ 用户中断操作")
        print("🛑 正在安全停止机械臂...")
        try:
            if robot_mover.socket_fd:
                nrc.robot_stop(robot_mover.socket_fd)
        except:
            pass
    except Exception as e:
        print(f"\n❌ 执行过程中发生异常: {e}")
        print("🛑 正在尝试安全停止...")
    finally:
        # 8. 清理资源
        robot_mover.disconnect()

    print("=" * 60)

if __name__ == "__main__":
    main()
